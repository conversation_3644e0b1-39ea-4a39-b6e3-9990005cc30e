import React, { useState, useEffect } from "react";
import { travelService, TravelMode } from "../../services/api/travel.service";
import { FaClock, FaShip, FaFlag, FaCoins, FaBolt } from "react-icons/fa";
import useUserDataStore from "../../store/useUserDataStore";

const TravelTimeEstimator = ({ destinationRegionId, onTravelModeChange, onEstimateChange, disabled = false }) => {
  const [estimate, setEstimate] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [travelMode, setTravelMode] = useState(TravelMode.REGULAR);
  const { userData } = useUserDataStore();

  useEffect(() => {
    const fetchEstimate = async () => {
      if (!userData?.region?.id || !destinationRegionId) {
        setLoading(false);
        return;
      }

      try {
        setLoading(true);
        setError(null);

        const result = await travelService.getTravelTimeEstimate({
          originRegionId: userData.region.id,
          destinationRegionId,
          travelMode
        });

        setEstimate(result);

        // Notify parent component of the estimate change
        if (onEstimateChange) {
          onEstimateChange(result);
        }
      } catch (err) {
        console.error("Error fetching travel time estimate:", err);
        setError("Failed to calculate travel time");
      } finally {
        setLoading(false);
      }
    };

    fetchEstimate();
  }, [userData?.region?.id, destinationRegionId, travelMode]);

  const handleTravelModeChange = (mode) => {
    setTravelMode(mode);
    if (onTravelModeChange) {
      onTravelModeChange(mode);
    }
  };

  const canAffordTravel = () => {
    if (!estimate || !userData) return false;

    if (estimate.currency === 'money') {
      return userData.money >= estimate.cost;
    } else if (estimate.currency === 'gold') {
      return userData.gold >= estimate.cost;
    }

    return false;
  };

  if (loading) {
    return (
      <div className="bg-gray-700/30 rounded-lg p-3 animate-pulse">
        <div className="h-4 bg-gray-600 rounded w-3/4 mb-2"></div>
        <div className="h-4 bg-gray-600 rounded w-1/2"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-red-900/20 text-red-400 rounded-lg p-3 text-sm">
        {error}
      </div>
    );
  }

  if (!estimate) {
    return null;
  }

  // Format travel time in hours and minutes
  const formatTravelTime = (minutes) => {
    const hours = Math.floor(minutes / 60);
    const mins = minutes % 60;
    
    if (hours === 0) {
      return `${mins} minutes`;
    } else if (mins === 0) {
      return `${hours} hour${hours > 1 ? 's' : ''}`;
    } else {
      return `${hours} hour${hours > 1 ? 's' : ''} ${mins} minute${mins > 1 ? 's' : ''}`;
    }
  };

  return (
    <div className="bg-gray-700/30 rounded-lg p-4 text-sm space-y-4">
      {/* Travel Mode Selection */}
      <div>
        <h4 className="text-white font-medium mb-3">Travel Mode:</h4>
        <div className="flex space-x-3">
          <button
            onClick={() => !disabled && handleTravelModeChange(TravelMode.REGULAR)}
            disabled={disabled}
            className={`flex-1 px-4 py-3 rounded-lg text-sm font-medium transition-all duration-200 border-2 ${
              disabled
                ? 'bg-gray-600 text-gray-400 border-gray-500 cursor-not-allowed'
                : travelMode === TravelMode.REGULAR
                ? 'bg-blue-600 text-white border-blue-400 shadow-lg shadow-blue-600/30 ring-2 ring-blue-400/50'
                : 'bg-gray-700 text-gray-300 border-gray-600 hover:bg-gray-600 hover:border-gray-500 hover:text-white'
            }`}
          >
            <FaCoins className={`inline mr-2 ${
              disabled
                ? 'text-gray-500'
                : travelMode === TravelMode.REGULAR
                ? 'text-blue-200'
                : 'text-gray-400'
            }`} />
            Regular Travel
          </button>
          <button
            onClick={() => !disabled && handleTravelModeChange(TravelMode.SPEED)}
            disabled={disabled}
            className={`flex-1 px-4 py-3 rounded-lg text-sm font-medium transition-all duration-200 border-2 ${
              disabled
                ? 'bg-gray-600 text-gray-400 border-gray-500 cursor-not-allowed'
                : travelMode === TravelMode.SPEED
                ? 'bg-yellow-600 text-white border-yellow-400 shadow-lg shadow-yellow-600/30 ring-2 ring-yellow-400/50'
                : 'bg-gray-700 text-gray-300 border-gray-600 hover:bg-gray-600 hover:border-gray-500 hover:text-white'
            }`}
          >
            <FaBolt className={`inline mr-2 ${
              disabled
                ? 'text-gray-500'
                : travelMode === TravelMode.SPEED
                ? 'text-yellow-200'
                : 'text-gray-400'
            }`} />
            Speed Travel
          </button>
        </div>

        {/* Active Mode Indicator */}
        {/* <div className="mt-2 text-center">
          <span className={`inline-flex items-center px-3 py-1 rounded-full text-xs font-medium ${
            travelMode === TravelMode.REGULAR
              ? 'bg-blue-900/50 text-blue-300 border border-blue-600/50'
              : 'bg-yellow-900/50 text-yellow-300 border border-yellow-600/50'
          }`}>
            {travelMode === TravelMode.REGULAR ? (
              <>
                <FaCoins className="mr-1" />
                Regular Mode Selected
              </>
            ) : (
              <>
                <FaBolt className="mr-1" />
                Speed Mode Selected
              </>
            )}
          </span>
        </div> */}
      </div>

      {/* Travel Information */}
      <div className="space-y-2">
        <div className="flex items-center">
          <FaClock className="text-blue-400 mr-2" />
          <span className="text-gray-300">
            Travel time: <span className="text-white font-medium">{formatTravelTime(estimate.travelTime)}</span>
            {travelMode === TravelMode.SPEED && (
              <span className="text-yellow-400 text-xs ml-1">(50% faster)</span>
            )}
          </span>
        </div>

        <div className="flex items-center">
          <span className="text-gray-300 flex items-center">
            {estimate.seaCrossing ? (
              <>
                <FaShip className="text-blue-400 mr-2" />
                Includes sea crossing
              </>
            ) : (
              <>
                <FaFlag className={`${estimate.sameState ? 'text-green-400' : 'text-yellow-400'} mr-2`} />
                {estimate.sameState ? 'Same state' : 'Different state'}
              </>
            )}
          </span>
        </div>

        {estimate.distance > 0 && (
          <div className="text-gray-300">
            Distance: <span className="text-white font-medium">{estimate.distance.toFixed(1)} km</span>
          </div>
        )}

        {/* Cost Information */}
        <div className="flex items-center">
          {estimate.currency === 'gold' ? (
            <FaCoins className="text-yellow-400 mr-2" />
          ) : (
            <span>💰</span>
          )}
          <span className="text-gray-300">
            Cost: <span className={`font-medium ${canAffordTravel() ? 'text-white' : 'text-red-400'}`}>
              {estimate.cost} {estimate.currency}
            </span>
          </span>
        </div>

        {/* Insufficient Funds Warning */}
        {!canAffordTravel() && (
          <div className="bg-red-900/30 p-2 rounded-md">
            <p className="text-red-400 text-xs">
              Insufficient {estimate.currency}! You have{' '}
              {estimate.currency === 'gold' ? userData?.gold || 0 : userData?.money || 0}{' '}
              {estimate.currency}, but need {estimate.cost}.
            </p>
          </div>
        )}
      </div>
    </div>
  );
};

export default TravelTimeEstimator;
