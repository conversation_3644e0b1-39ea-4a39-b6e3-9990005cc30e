import React, { useState, useEffect } from "react";
import { useP<PERSON>ms, Link, useNavigate } from "react-router-dom";
import { regionService } from "../../services/api/region.service";
import { userService } from "../../services/api/user.service";
import { travelService, TravelMode } from "../../services/api/travel.service";
import { stateService } from "../../services/api/state.service";
import { showSuccessToast } from "../../utils/showSuccessToast";
import { showErrorToast } from "../../utils/showErrorToast";
import useUserDataStore from "../../store/useUserDataStore";
import TravelStatus from "../travel/TravelStatus";
import TravelTimeEstimator from "../travel/TravelTimeEstimator";
import TravelPermissionRequest from "../travel/TravelPermissionRequest";
import SearchableModal from "../common/SearchableModal";
import RevolutionWarModal from "../wars/RevolutionWarModal";
import { FaMapMarkedAlt, FaCrown, FaFistRaised, FaLock, FaBolt, FaCoins, FaInfoCircle } from "react-icons/fa";
import { Users } from "lucide-react";

const RegionDetail = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const [region, setRegion] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [movingToRegion, setMovingToRegion] = useState(false);
  const [currentTravel, setCurrentTravel] = useState(null);
  const [travelEstimate, setTravelEstimate] = useState(null);
  const [needsPermission, setNeedsPermission] = useState(false);
  const [permissionRequested, setPermissionRequested] = useState(false);
  const [userPermissions, setUserPermissions] = useState([]);
  const [isStateLeader, setIsStateLeader] = useState(false);
  const [ledState, setLedState] = useState(null);
  const [checkingLeadership, setCheckingLeadership] = useState(false);
  const [citizensModalOpen, setCitizensModalOpen] = useState(false);
  const [revolutionModalOpen, setRevolutionModalOpen] = useState(false);
  const [selectedTravelMode, setSelectedTravelMode] = useState(TravelMode.REGULAR);
  const [currentTravelEstimate, setCurrentTravelEstimate] = useState(null);
  const { userData, fetchUserData, updateUserData } = useUserDataStore();

  // Function to check if user is a state leader
  const checkStateLeadership = async () => {
    try {
      setCheckingLeadership(true);
      // Get state led by the current user (if any)
      const state = await stateService.getStateLedByUser();
      setLedState(state);

      // If the user leads a state, they're a state leader
      setIsStateLeader(!!state);

      if (state) {
        console.log("User is the leader of state:", state.name);
      }

      return state;
    } catch (err) {
      console.error("Error checking state leadership:", err);
      return null;
    } finally {
      setCheckingLeadership(false);
    }
  };

  // Render function for player items
  const renderPlayerItem = (player) => (
    <div>
      <div className="flex items-center justify-between mb-3">
        <div className="flex items-center gap-3">
          <div className="w-10 h-10 bg-gray-700 rounded-full flex items-center justify-center text-lg text-neonBlue">
            {player?.avatarUrl ? (
              <img
                src={player.avatarUrl}
                alt="Profile Avatar"
                className="w-full h-full object-cover rounded-full"
              />
            ) : (
              player?.username?.charAt(0).toUpperCase() || "U"
            )}
          </div>
          <div>
            <h3 className="text-lg font-semibold text-white">
              {player.username}
            </h3>
            <div className="flex items-center gap-2 text-sm">
              <span className="text-gray-400">Level:</span>
              <span className="text-white font-medium">
                {player.level || 0}
              </span>
            </div>
          </div>
        </div>
        {player.isPremium && (
          <span className="text-yellow-400 text-xs px-2 py-0.5 bg-yellow-900/30 rounded-full">
            PREMIUM
          </span>
        )}
      </div>
    </div>
  );

  // Handle citizen click to navigate to user profile
  const handleCitizenClick = (citizen) => {
    // We'll use the Link component or navigate programmatically
    navigate(`/users/${citizen.id}`);
  };

  // Open citizens modal
  const openCitizensModal = () => {
    setCitizensModalOpen(true);
  };

  // Close citizens modal
  const closeCitizensModal = () => {
    setCitizensModalOpen(false);
  };

  // Revolution war functions
  const canStartRevolution = () => {
    if (!userData || !region || !isUserInRegion) return false;

    // Check if user is a citizen of this region
    const isUserCitizen = region.users?.some(user => user.id === userData.id);
    if (!isUserCitizen) return false;

    // Check user level (must be 5+)
    if (userData.level < 5) return false;

    // Check user has enough gold (500)
    if (userData.gold < 500) return false;

    // Check revolution cooldown (4 days)
    if (region.lastRevolution) {
      const lastRevolutionDate = new Date(region.lastRevolution);
      const fourDaysAgo = new Date();
      fourDaysAgo.setDate(fourDaysAgo.getDate() - 4);
      if (lastRevolutionDate > fourDaysAgo) return false;
    }

    return true;
  };

  const openRevolutionModal = () => {
    setRevolutionModalOpen(true);
  };

  const closeRevolutionModal = () => {
    setRevolutionModalOpen(false);
  };

  const handleRevolutionSuccess = (war) => {
    // Refresh region data
    const refreshRegion = async () => {
      try {
        const regionData = await regionService.getRegion(id);
        setRegion(regionData);
        fetchUserData(); // Refresh user data to update gold
      } catch (err) {
        console.error("Error refreshing region data:", err);
      }
    };

    refreshRegion();
    closeRevolutionModal();

    // Navigate to war details
    if (war && war.id) {
      navigate(`/wars/${war.id}`);
    }
  };

useEffect(() => {
  const init = async () => {
    try {
      setLoading(true);
      const [regionData, state] = await Promise.all([
        regionService.getRegion(id),
        stateService.getStateLedByUser(),
      ]);
      setRegion(regionData);
      setLedState(state);
      setIsStateLeader(!!state);
    } catch (err) {
      console.error("Error during init:", err);
      showErrorToast("Failed to load region details. Please try again later.");
    } finally {
      setLoading(false);
      setCheckingLeadership(false);
    }
  };

  init();
}, [id]);


  // Separate useEffect for travel data to avoid unnecessary fetches
  useEffect(() => {
    // Only fetch travel data if user is logged in and has a region
    if (!userData?.region?.id) return;

    const fetchTravelData = async () => {
      try {
        // Get current travel status - this is essential
        const travel = await travelService.getCurrentTravel();
        setCurrentTravel(travel);

        // If user is already traveling, don't check for permissions
        if (
          travel &&
          (travel.status?.toLowerCase() === "in_progress" ||
            travel.status === "IN_PROGRESS")
        ) {
          return;
        }

        // Only fetch permissions and estimates when needed
        // These are less critical and can be fetched only when viewing a different region
        if (userData.region.id !== id) {
          // Check if user has any pending permission requests for this region
          const permissions = await travelService.getUserPermissionRequests();
          setUserPermissions(permissions);

          const pendingPermission = permissions.find(
            (p) =>
              p.destinationRegion?.id === id &&
              (p.status?.toLowerCase() === "pending" || p.status === "PENDING")
          );

          if (pendingPermission) {
            setPermissionRequested(true);
          }

          // Check if travel requires permission
          const estimate = await travelService.getTravelTimeEstimate({
            originRegionId: userData.region.id,
            destinationRegionId: id,
          });

          setTravelEstimate(estimate);

          // Check if the destination region is independent (doesn't belong to a state)
          const isIndependentRegion = !region?.state;

          // Check if user is a leader of the destination state
          const isLeaderOfDestinationState =
            ledState && ledState?.id === region?.state?.id;
          console.log(isLeaderOfDestinationState, "isLeaderOfDestinationState");

          // Check if the destination state has open borders
          const hasOpenBorders = region?.state?.hasOpenBorders;

          // No permission needed if:
          // 1. Regions are in the same state, OR
          // 2. Destination region is independent, OR
          // 3. User is the leader of the destination state, OR
          // 4. Destination state has open borders
          if (
            estimate.sameState ||
            isIndependentRegion ||
            isLeaderOfDestinationState ||
            hasOpenBorders
          ) {
            setNeedsPermission(false);
          } else {
            // Otherwise, permission is needed
            setNeedsPermission(true);
          }
        }
      } catch (err) {
        showErrorToast(err);
      }
    };

    fetchTravelData();
  }, [id, userData?.region?.id, userData?.state?.id, region?.state?.id]);

  const handleMoveToRegion = async () => {
    try {
      setMovingToRegion(true);

      // Check if the destination region is independent (doesn't belong to a state)
      const isIndependentRegion = !region?.state;

      // Check if user is a leader of the destination state
      const isLeaderOfDestinationState =
        ledState && ledState.id === region?.state?.id;

      // Check if the destination state has open borders
      const hasOpenBorders = region?.state?.hasOpenBorders;

      // Skip permission checks for independent regions, if user is leader of destination state, or if state has open borders
      if (!isIndependentRegion && !isLeaderOfDestinationState && !hasOpenBorders) {
        // If we need permission and haven't requested it yet, show error
        if (needsPermission && !permissionRequested) {
          showErrorToast("You need permission to travel to this region.");
          return;
        }

        // If we have a pending permission request, check its status
        if (permissionRequested) {
          const pendingRequest = userPermissions.find(
            (p) =>
              p.destinationRegion?.id === id &&
              (p.status?.toLowerCase() === "pending" || p.status === "PENDING")
          );

          if (pendingRequest) {
            showErrorToast(
              "Your permission request is still pending approval."
            );
            return;
          }
        }

        // Check if we have an approved permission
        const hasApprovedPermission = userPermissions.some(
          (p) =>
            p.destinationRegion?.id === id &&
            (p.status?.toLowerCase() === "approved" || p.status === "APPROVED")
        );

        // If we need permission but don't have approval, show error
        if (needsPermission && !hasApprovedPermission) {
          showErrorToast("Your travel request was not approved.");
          return;
        }
      }

      // Find approved permission if needed
      let permissionId = undefined;
      if (needsPermission) {
        const approvedPermission = userPermissions.find(
          (p) =>
            p.destinationRegion?.id === id &&
            (p.status?.toLowerCase() === "approved" || p.status === "APPROVED")
        );
        permissionId = approvedPermission?.id;
      }

      // Initiate travel
      await travelService.initiateTravel({
        destinationRegionId: id,
        permissionId,
        travelMode: selectedTravelMode
      });
      showSuccessToast(
        "Travel initiated! You are now on your way to this region."
      );

      // Refresh travel data
      const travel = await travelService.getCurrentTravel();
      setCurrentTravel(travel);
      // Refresh user data
      fetchUserData(true);
    } catch (err) {
      console.error("Error initiating travel:", err);
      showErrorToast(
        err || "Failed to initiate travel. Please try again later."
      );
    } finally {
      setMovingToRegion(false);
    }
  };

  const handlePermissionRequested = async () => {
    setPermissionRequested(true);
    const permissions = await travelService.getUserPermissionRequests();
    setUserPermissions(permissions);
  };

  const isUserInRegion = userData?.region?.id === id;

  if (loading)
    return (
      <div className="flex justify-center items-center min-h-screen bg-gray-900">
        <div className="loader text-blue-500">Loading...</div>
      </div>
    );

  if (error)
    return (
      <div className="min-h-screen bg-gray-900 p-4">
        <div className="text-red-500 bg-red-900/20 p-4 rounded-lg">{error}</div>
      </div>
    );

  if (!region)
    return (
      <div className="min-h-screen bg-gray-900 p-4">
        <div className="text-gray-300 bg-gray-800/50 p-4 rounded-lg">
          Region not found
        </div>
      </div>
    );

  return (
    <div className="min-h-screen bg-gray-900 p-4">
      <div className="max-w-6xl mx-auto">
        <div className="mb-6">
          {/* <Link to="/regions" className="text-blue-500 hover:text-blue-400 flex items-center">
            <span className="mr-2">←</span> Back to Regions
          </Link> */}
        </div>

        <div className="bg-gray-800 rounded-lg shadow-xl p-6">
          {/* Header Section */}
          <div className="flex items-center mb-8">
            {region.imageUrl && (
              <img
                src={region.imageUrl}
                alt={`${region.name}`}
                className="w-20 h-20 object-cover rounded-lg mr-6 border-2 border-gray-700"
              />
            )}
            <div>
              <h1 className="text-4xl font-bold text-white mb-2">
                {region.name}
              </h1>
              {region.state && (
                <p className="text-gray-400">
                  Part of{" "}
                  <Link
                    to={`/states/${region.state.id}`}
                    className="text-blue-400 hover:text-blue-300"
                  >
                    {region.state.name}
                  </Link>
                </p>
              )}
            </div>
          </div>

          {/* Main Content Grid */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            {/* Left Column */}
            <div>
              {/* Travel Section */}
              <div className="bg-gray-700/30 rounded-lg p-6 mb-6">
                <h2 className="text-xl font-semibold text-white mb-4 flex items-center">
                  <FaMapMarkedAlt className="mr-2 text-blue-400" /> Travel
                  Status
                </h2>

                {isUserInRegion ? (
                  <div className="space-y-4">
                    <div className="bg-green-900/30 p-4 rounded-lg text-center">
                      <p className="text-green-400 font-medium">
                        You are currently in this region
                      </p>
                    </div>

                    {/* Revolution War Button */}
                    <div className="bg-gray-700/30 rounded-lg p-4">
                      <h3 className="text-white font-medium mb-3 flex items-center">
                        <FaFistRaised className="mr-2 text-red-400" />
                        Revolution War
                      </h3>
                      <p className="text-gray-300 text-sm mb-4">
                        Start a revolution to create an independent state from this region.
                      </p>

                      {canStartRevolution() ? (
                        <button
                          onClick={openRevolutionModal}
                          className="w-full bg-red-600 hover:bg-red-700 text-white font-medium py-2 px-4 rounded-md transition-colors"
                        >
                          Start Revolution (500 Gold)
                        </button>
                      ) : (
                        <div className="space-y-2">
                          <button
                            disabled
                            className="w-full bg-gray-600 text-gray-400 font-medium py-2 px-4 rounded-md cursor-not-allowed"
                          >
                            Revolution Unavailable
                          </button>
                          <div className="text-xs text-gray-400 space-y-1">
                            {!userData || userData.level < 5 && (
                              <p>• Requires level 5+</p>
                            )}
                            {!userData || userData.gold < 500 && (
                              <p>• Requires 500 gold</p>
                            )}
                            {!region?.users?.some(user => user.id === userData?.id) && (
                              <p>• Must be a citizen of this region</p>
                            )}
                            {region?.lastRevolution && (() => {
                              const lastRevolutionDate = new Date(region.lastRevolution);
                              const fourDaysAgo = new Date();
                              fourDaysAgo.setDate(fourDaysAgo.getDate() - 4);
                              return lastRevolutionDate > fourDaysAgo;
                            })() && (
                              <p>• Revolution cooldown active (4 days)</p>
                            )}
                          </div>
                        </div>
                      )}
                    </div>
                  </div>
                ) : currentTravel &&
                  (currentTravel.status?.toLowerCase() === "in_progress" ||
                    currentTravel.status === "IN_PROGRESS") ? (
                  <div>
                    {/* Embed TravelStatus component directly when traveling */}
                    <TravelStatus onTravelComplete={fetchUserData} />
                  </div>
                ) : (
                  <div>
                    {/* Special Status Messages */}
                    {isStateLeader &&
                      ledState &&
                      ledState.id === region?.state?.id && (
                        <div className="bg-green-900/30 p-4 rounded-lg text-center mb-4">
                          <p className="text-green-400 font-medium flex items-center justify-center">
                            <FaCrown className="mr-2 text-yellow-400" />
                            You are the leader of this state and can travel
                            freely
                          </p>
                        </div>
                      )}

                    {!region?.state && (
                      <div className="bg-blue-900/30 p-4 rounded-lg text-center mb-4">
                        <p className="text-blue-400 font-medium flex items-center justify-center">
                          <FaMapMarkedAlt className="mr-2 text-blue-400" />
                          This is an independent region - no travel permission
                          required
                        </p>
                      </div>
                    )}

                    {region?.state?.hasOpenBorders && (
                      <div className="bg-green-900/30 p-4 rounded-lg text-center mb-4">
                        <p className="text-green-400 font-medium flex items-center justify-center">
                          <FaMapMarkedAlt className="mr-2 text-green-400" />
                          This state has open borders - no travel permission
                          required
                        </p>
                      </div>
                    )}

                    {/* Travel Process Steps */}
                    <div className="space-y-6">
                      {/* Step 1: Travel Mode Selection */}
                      <div className="bg-gray-800/30 p-4 rounded-lg">
                        <div className="flex items-center mb-3">
                          <div className="w-8 h-8 bg-blue-600 text-white rounded-full flex items-center justify-center text-sm font-bold mr-3">
                            1
                          </div>
                          <h3 className="text-white font-medium">
                            Select Your Travel Mode
                          </h3>
                        </div>
                        <p className="text-gray-300 text-sm mb-4 ">
                          {permissionRequested
                            ? "Travel mode locked - you have already submitted a permission request with your selected mode."
                            : "Choose between regular travel (using money) or speed travel (using gold for 50% faster arrival)."
                          }
                        </p>
                        <div className="">
                          <TravelTimeEstimator
                            destinationRegionId={id}
                            onTravelModeChange={setSelectedTravelMode}
                            onEstimateChange={setCurrentTravelEstimate}
                            disabled={permissionRequested}
                          />
                        </div>
                      </div>

                      {/* Step 2: Permission Request (if needed) */}
                      {needsPermission && (
                        <div className="bg-gray-800/30 p-4 rounded-lg">
                          <div className="flex items-center mb-3">
                            <div className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-bold mr-3 ${
                              permissionRequested
                                ? 'bg-green-600 text-white'
                                : 'bg-yellow-600 text-white'
                            }`}>
                              2
                            </div>
                            <h3 className="text-white font-medium">
                              Request Travel Permission
                            </h3>
                          </div>
                          <p className="text-gray-300 text-sm mb-4 ">
                            Since this region requires permission, you must request approval from the state leader. Once approved, travel will start automatically.
                          </p>
                          <div className="">
                            {!permissionRequested ? (
                              <TravelPermissionRequest
                                regionId={id}
                                regionName={region?.name || "this region"}
                                travelMode={selectedTravelMode}
                                travelCost={currentTravelEstimate?.cost}
                                travelCurrency={currentTravelEstimate?.currency}
                                onPermissionRequested={handlePermissionRequested}
                              />
                            ) : (
                              <div className="space-y-4">
                                <div className="bg-yellow-900/30 p-4 rounded-lg">
                                  <div className="flex items-center justify-center mb-3">
                                    <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-yellow-400 mr-3"></div>
                                    <p className="text-yellow-400 font-medium">
                                      Permission request submitted and pending approval
                                    </p>
                                  </div>
                                  <div className="text-center space-y-2">
                                    <p className="text-yellow-300 text-sm">
                                      Waiting for state leader to respond to your request.
                                    </p>
                                    <p className="text-yellow-300 text-sm">
                                      <strong>Travel will start automatically once approved.</strong>
                                    </p>
                                  </div>
                                </div>

                                {/* Show locked travel details */}
                                <div className="bg-gray-700/50 p-4 rounded-lg border border-gray-600">
                                  <h4 className="text-white font-medium mb-2 flex items-center">
                                    <FaLock className="mr-2 text-gray-400" />
                                    Requested Travel Details (Locked)
                                  </h4>
                                  <div className="space-y-2 text-sm">
                                    <div className="flex justify-between">
                                      <span className="text-gray-300">Travel Mode:</span>
                                      <span className="text-white flex items-center">
                                        {selectedTravelMode === TravelMode.SPEED ? (
                                          <>
                                            <FaBolt className="mr-1 text-yellow-400" />
                                            Speed Travel
                                          </>
                                        ) : (
                                          <>
                                            <FaCoins className="mr-1 text-blue-400" />
                                            Regular Travel
                                          </>
                                        )}
                                      </span>
                                    </div>
                                    {currentTravelEstimate && (
                                      <>
                                        <div className="flex justify-between">
                                          <span className="text-gray-300">Cost Paid:</span>
                                          <span className="text-white flex items-center">
                                            {currentTravelEstimate.currency === 'gold' ? (
                                              // <FaGem className="mr-1 text-yellow-400" />
                                              <FaCoins className="mr-1 text-green-400" />
                                            ) : (
                                              <span>💰</span>
                                            )}
                                            {currentTravelEstimate.cost} {currentTravelEstimate.currency}
                                          </span>
                                        </div>
                                        <div className="flex justify-between">
                                          <span className="text-gray-300">Travel Time:</span>
                                          <span className="text-white">
                                            {Math.floor(currentTravelEstimate.travelTime / 60)}h {currentTravelEstimate.travelTime % 60}m
                                          </span>
                                        </div>
                                      </>
                                    )}
                                  </div>
                                </div>
                              </div>
                            )}
                          </div>
                        </div>
                      )}

                      {/* Step 3: Automatic Travel (for regions requiring permission) */}
                      {needsPermission && permissionRequested && (
                        <div className="bg-gray-800/30 p-4 rounded-lg">
                          <div className="flex items-center mb-3">
                            <div className="w-8 h-8 bg-gray-600 text-gray-400 rounded-full flex items-center justify-center text-sm font-bold mr-3">
                              3
                            </div>
                            <h3 className="text-white font-medium">
                              Automatic Travel
                            </h3>
                          </div>
                          <p className="text-gray-300 text-sm mb-4 ">
                            Travel will begin automatically once your permission is approved by the state leader.
                          </p>
                          <div className="">
                            <div className="bg-blue-900/30 p-4 rounded-lg text-center">
                              <p className="text-blue-300 text-sm">
                                <FaInfoCircle className="inline mr-2" />
                                No action required - the system will handle travel initiation upon approval.
                              </p>
                            </div>
                          </div>
                        </div>
                      )}

                      {/* Direct Travel Button (for regions NOT requiring permission) */}
                      {!needsPermission && (
                        <div className="bg-gray-800/30 p-4 rounded-lg">
                          <div className="flex items-center mb-3">
                            <div className="w-8 h-8 bg-blue-600 text-white rounded-full flex items-center justify-center text-sm font-bold mr-3">
                              2
                            </div>
                            <h3 className="text-white font-medium">
                              Initiate Travel
                            </h3>
                          </div>
                          <p className="text-gray-300 text-sm mb-4 ">
                            Start your journey to this region immediately.
                          </p>
                          <div className="">
                            <button
                              onClick={handleMoveToRegion}
                              disabled={movingToRegion || currentTravel}
                              className={`px-6 py-3 rounded-md font-medium transition-colors ${
                                movingToRegion || currentTravel
                                  ? 'bg-gray-600 text-gray-400 cursor-not-allowed'
                                  : 'bg-blue-600 hover:bg-blue-700 text-white'
                              }`}
                            >
                              {movingToRegion
                                ? "Initiating Travel..."
                                : currentTravel
                                ? "Already Traveling..."
                                : "Travel to this Region"}
                            </button>
                          </div>
                        </div>
                      )}
                    </div>
                  </div>
                )}
              </div>
              {/* <div className="bg-gray-700/30 rounded-lg p-6 mb-6">
                <h2 className="text-xl font-semibold text-white mb-4">Region Details</h2>
                <div className="space-y-3">
                  <div className="flex justify-between">
                    <span className="text-gray-400">Population:</span>
                    <span className="text-white">
                      {typeof region.population === 'object'
                        ? `${region.population.current?.toLocaleString() || 0}/${region.population.max?.toLocaleString() || 0}`
                        : region.population?.toLocaleString() || 'Unknown'}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-400">Development Level:</span>
                    <span className="text-white">
                      {typeof region.developmentLevel === 'object'
                        ? JSON.stringify(region.developmentLevel)
                        : region.developmentLevel || 'Unknown'}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-400">Terrain:</span>
                    <span className="text-white">{region.terrain || 'Unknown'}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-400">Climate:</span>
                    <span className="text-white">{region.climate || 'Unknown'}</span>
                  </div>
                </div>
              </div> */}

              {/* Resources Section */}
              {/* <div className="bg-gray-700/30 rounded-lg p-6">
                <h2 className="text-xl font-semibold text-white mb-4">Resources</h2>
                <div className="grid grid-cols-2 gap-4">
                  {region.resources ? (
                    Object.entries(region.resources).map(([resource, amount]) => (
                      <div key={resource} className="bg-gray-800 p-3 rounded-lg">
                        <div className="text-gray-400 text-sm">{resource.charAt(0).toUpperCase() + resource.slice(1)}</div>
                        <div className="text-white font-medium">
                          {typeof amount === 'object' && amount.current !== undefined && amount.max !== undefined
                            ? `${amount.current}/${amount.max}`
                            : amount}
                        </div>
                      </div>
                    ))
                  ) : (
                    <p className="text-gray-400 col-span-2">No resource information available</p>
                  )}
                </div>
              </div> */}
            </div>

            {/* Right Column */}
            <div>
              {/* Citizens Section */}
              <div className="bg-gray-700/30 rounded-lg p-6 mb-6">
                <div className="flex items-center justify-between mb-4">
                  <h2 className="text-xl font-semibold text-white flex items-center">
                    <Users className="w-5 h-5 text-neonBlue mr-2" />
                    Citizens ({region.users?.length || 0})
                  </h2>
                  {region.users && region.users.length > 0 && (
                    <button
                      onClick={openCitizensModal}
                      className="text-neonBlue hover:text-blue-400 text-sm font-medium transition-colors"
                    >
                      View All
                    </button>
                  )}
                </div>
                {region.users && region.users.length > 0 ? (
                  <div className="space-y-3">
                    {region.users.slice(0, 5).map((user) => (
                      <div
                        key={user.id}
                        className="bg-gray-800 p-3 rounded-lg flex justify-between items-center hover:bg-gray-750 transition-colors"
                      >
                        <div className="flex items-center gap-3">
                          <div className="w-8 h-8 bg-gray-700 rounded-full flex items-center justify-center text-sm text-neonBlue">
                            {user?.avatarUrl ? (
                              <img
                                src={user.avatarUrl}
                                alt="Profile Avatar"
                                className="w-full h-full object-cover rounded-full"
                              />
                            ) : (
                              user?.username?.charAt(0).toUpperCase() || "U"
                            )}
                          </div>
                          <div>
                            <div className="text-white flex items-center gap-2">
                              {user.username}
                              {user.isPremium && (
                                <span className="text-yellow-400 text-xs px-2 py-0.5 bg-yellow-900/30 rounded-full">
                                  PREMIUM
                                </span>
                              )}
                            </div>
                            <div className="text-gray-400 text-sm">
                              Level: {user.level || 0}
                            </div>
                          </div>
                        </div>
                        <Link
                          to={`/users/${user.id}`}
                          className="text-neonBlue hover:text-blue-400 text-sm font-medium transition-colors"
                        >
                          View
                        </Link>
                      </div>
                    ))}
                    {region.users.length > 5 && (
                      <div className="text-center mt-3">
                        <button
                          onClick={openCitizensModal}
                          className="text-neonBlue hover:text-blue-400 text-sm font-medium transition-colors"
                        >
                          View {region.users.length - 5} more citizens
                        </button>
                      </div>
                    )}
                  </div>
                ) : (
                  <p className="text-gray-400">No citizens in this region.</p>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Citizens Modal */}
      <SearchableModal
        isOpen={citizensModalOpen}
        onClose={closeCitizensModal}
        title={`Citizens of ${region?.name || "Region"}`}
        icon={Users}
        data={region?.users || []}
        loading={false}
        onItemClick={handleCitizenClick}
        renderItem={renderPlayerItem}
        searchPlaceholder="Search citizens by username"
      />

      {/* Revolution War Modal */}
      <RevolutionWarModal
        isOpen={revolutionModalOpen}
        onClose={closeRevolutionModal}
        region={region}
        userData={userData}
        onSuccess={handleRevolutionSuccess}
      />
    </div>
  );
};

export default RegionDetail;
