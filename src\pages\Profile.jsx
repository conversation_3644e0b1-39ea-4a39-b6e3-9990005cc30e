import React, { useState, useEffect, useRef } from "react";
import Navbar from "../components/Navbar";
import api from "../services/api/api";
import { useAuthGuard } from "../hooks/useAuthGuard";
import { showErrorToast } from "../utils/showErrorToast";
import { showSuccessToast } from "../utils/showSuccessToast";
import { useCountdown } from "../hooks/useCountdown";
import { calculateTrainingTime } from "../utils/calculateTrainingTime";
import { calculateTrainingCost } from "../utils/calculateTrainingCost";
import useAuthStore from "../store/useAuthStore";
import useUserDataStore from "../store/useUserDataStore";
import { useNavigate } from "react-router-dom";
import { Link } from "react-router-dom";
import CreateStateModal from "../components/states/CreateStateModal";
import CreatePartyModal from "../components/parties/CreatePartyModal";
import UserWarStats from "../components/analytics/UserWarStats.tsx";
import { useUserResources } from "../hooks/useUserResources";
import {
  FaCrown,
  FaEdit,
  FaCheck,
  FaTimes,
  FaShare,
  FaCopy,
  FaCamera,
  FaTrash,
  FaUser,
} from "react-icons/fa";
import { userService } from "../services/api/user.service";
import { stateService } from "../services/api/state.service";
import Footer from "../components/common/Footer";

export default function Profile() {
  useAuthGuard();
  const { user } = useAuthStore();
  const navigate = useNavigate();
  const {
    userData: profile,
    loading: userDataLoading,
    fetchUserData,
  } = useUserDataStore();
  const [loading, setLoading] = useState(true);
  const [region, setRegion] = useState(null);
  const [countdown, setCountdown] = useState(null);
  const [myState, setMyState] = useState(null);
  const [myWars, setMyWars] = useState([]);
  const trainingCompletedShown = useRef(false);
  const [isCreateStateModalOpen, setIsCreateStateModalOpen] = useState(false);
  const [isCreatePartyModalOpen, setIsCreatePartyModalOpen] = useState(false);
  const { refetch: refetchResources } = useUserResources();
  const [isEditingUsername, setIsEditingUsername] = useState(false);
  const [newUsername, setNewUsername] = useState("");
  const [isUpdatingUsername, setIsUpdatingUsername] = useState(false);
  const [showShareModal, setShowShareModal] = useState(false);
  const [copySuccess, setCopySuccess] = useState(false);
  const [isUploadingAvatar, setIsUploadingAvatar] = useState(false);
  const [isDeletingAvatar, setIsDeletingAvatar] = useState(false);
  const fileInputRef = useRef(null);

  const isTraining = !!countdown;

  useEffect(() => {
    if (!user?.id) return;

    const fetchData = async () => {
      try {
        // Fetch user data using the centralized store (use cache if available)
        await fetchUserData(false);

        if (profile?.region) {
          setRegion(profile.region);
        }
        // Fetch state data using dedicated endpoint
        try {
          const stateRes = await stateService.getUserState();

          setMyState(stateRes);
        } catch (error) {
          // User doesn't have a state, which is fine
          setMyState(null);
        }

        // Fetch wars data
        let myWars = [];
        try {
          const warsRes = await api.get("/wars/my-wars");
          myWars = warsRes.data;
        } catch (error) {
          // User doesn't have wars, which is fine
        }
        setMyWars(myWars);

        // Check if there's active training and set countdown
        if (profile?.trainingExpiresAt) {
          const now = Date.now();
          const target = new Date(profile.trainingExpiresAt).getTime();
          if (target > now) {
            // Only set up countdown for future expiry dates
            const diff = target - now;
            const hours = Math.floor(diff / 1000 / 60 / 60);
            const minutes = Math.floor((diff / 1000 / 60) % 60);
            const seconds = Math.floor((diff / 1000) % 60);

            if (hours === 0 && minutes === 0) {
              setCountdown(`${seconds}s`);
            } else if (hours === 0) {
              setCountdown(`${minutes}m ${seconds}s`);
            } else {
              setCountdown(`${hours}h ${minutes}m ${seconds}s`);
            }
          }
        }
      } catch (error) {
        showErrorToast(error);
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [user?.id]); // Keep using the correct dependency

  // This useEffect handles the countdown timer and completion
  useEffect(() => {
    if (!profile?.trainingExpiresAt) return;

    // Check if the training is already expired
    const now = Date.now();
    const target = new Date(profile.trainingExpiresAt).getTime();

    // If training has already expired, don't set up the countdown
    if (target <= now) return;

    let intervalId;
    const isActiveTraining = true; // Flag to track this specific training session

    // We no longer need to calculate progress or duration since we're not updating it
    // in the interval to avoid re-renders

    const updateCountdown = () => {
      const currentTime = Date.now();
      const timeRemaining = target - currentTime;

      if (timeRemaining <= 0) {
        setCountdown(null);
        clearInterval(intervalId);

        // Only show completion toast if this was an active training session
        // that completed during this component's lifecycle
        if (isActiveTraining) {
          showSuccessToast("Training completed!");
          refetchProfile();
        }
      } else {
        const hours = Math.floor(timeRemaining / 1000 / 60 / 60);
        const minutes = Math.floor((timeRemaining / 1000 / 60) % 60);
        const seconds = Math.floor((timeRemaining / 1000) % 60);

        // Special formatting when less than a minute remains
        if (hours === 0 && minutes === 0) {
          setCountdown(`${seconds}s`);
        } else if (hours === 0) {
          setCountdown(`${minutes}m ${seconds}s`);
        } else {
          setCountdown(`${hours}h ${minutes}m ${seconds}s`);
        }

        // We don't update the store here anymore to prevent re-renders
        // The progress bar will still show the initial progress value
        // and will be updated when the training completes via refetchProfile()
      }
    };

    updateCountdown();
    intervalId = setInterval(updateCountdown, 1000);

    return () => {
      clearInterval(intervalId);
    };
    // Only run this effect when trainingExpiresAt changes
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [profile?.trainingExpiresAt]);

  const refetchProfile = async () => {
    try {
      // Force refresh when explicitly called
      await fetchUserData(true);
    } catch (error) {
      showErrorToast("Failed to refresh profile.");
    }
  };

  // Username editing functions
  const handleEditUsername = () => {
    setNewUsername(profile?.username || "");
    setIsEditingUsername(true);
  };

  const handleCancelEdit = () => {
    setIsEditingUsername(false);
    setNewUsername("");
  };

  const handleSaveUsername = async () => {
    if (!newUsername.trim()) {
      showErrorToast("Username cannot be empty");
      return;
    }

    if (newUsername === profile?.username) {
      setIsEditingUsername(false);
      return;
    }

    setIsUpdatingUsername(true);
    try {
      await userService.updateProfile({
        username: newUsername.trim(),
        id: profile.id,
      });
      showSuccessToast("Username updated successfully!");

      // Force refresh the user data to reflect the change
      await fetchUserData(true);

      setIsEditingUsername(false);
      setNewUsername("");
    } catch (error) {
      showErrorToast(error || "Failed to update username");
    } finally {
      setIsUpdatingUsername(false);
    }
  };

  const handleUsernameKeyPress = (e) => {
    if (e.key === "Enter") {
      handleSaveUsername();
    } else if (e.key === "Escape") {
      handleCancelEdit();
    }
  };

  // Share profile functions
  const getShareableProfileUrl = () => {
    return `${window.location.origin}/users/${profile?.id}`;
  };

  const handleCopyProfileUrl = async () => {
    try {
      const url = getShareableProfileUrl();
      await navigator.clipboard.writeText(url);
      setCopySuccess(true);
      showSuccessToast("Profile URL copied to clipboard!");
      setTimeout(() => setCopySuccess(false), 2000);
    } catch (error) {
      showErrorToast("Failed to copy URL to clipboard");
    }
  };

  const handleShareProfile = () => {
    setShowShareModal(true);
  };

  // Avatar upload functions
  const handleAvatarClick = () => {
    fileInputRef.current?.click();
  };

 const handleFileSelect = async (event) => {
    const file = event.target.files?.[0];
    if (!file) return;

    // Client-side validation
    const allowedTypes = ['image/jpeg', 'image/png', 'image/webp'];
    if (!allowedTypes.includes(file.type)) {
      alert('Please select a JPEG, PNG, or WebP image file.');
      return;
    }

    const maxSize = 5 * 1024 * 1024; // 5MB
    if (file.size > maxSize) {
      alert('File size must be less than 5MB.');
      return;
    }

    setIsUploadingAvatar(true);

    try {
      const result = await userService.uploadAvatar(file);

      // Update profile with new avatar URL
      profile.avatarUrl = result.avatarUrl;

      console.log('Avatar uploaded successfully:', result);
    } catch (error) {
      console.error('Error uploading avatar:', error);
      alert(error.message || 'Failed to upload avatar. Please try again.');
    } finally {
      setIsUploadingAvatar(false);
      // Clear the file input
      if (fileInputRef.current) {
        fileInputRef.current.value = '';
      }
    }
  };

  const handleDeleteAvatar = async () => {
    if (!profile?.avatarUrl) return;

    const confirmed = window.confirm('Are you sure you want to delete your profile picture?');
    if (!confirmed) return;

    setIsDeletingAvatar(true);

    try {
      await userService.deleteAvatar();
      
      // Remove avatar URL from profile
      setProfile(prev => ({
        ...prev,
        avatarUrl: null
      }));

      console.log('Avatar deleted successfully');
    } catch (error) {
      console.error('Error deleting avatar:', error);
      alert(error.message || 'Failed to delete avatar. Please try again.');
    } finally {
      setIsDeletingAvatar(false);
    }
  };

  function formatTime(totalSeconds) {
    const hours = Math.floor(totalSeconds / 3600);
    const minutes = Math.floor((totalSeconds % 3600) / 60);
    const seconds = totalSeconds % 60;
    return `${hours}h ${minutes}m ${seconds}s`;
  }

  const handleTrain = async (perkType, durationMinutes, currency, amount) => {
    try {
      await api.post("/users/train", {
        trainingType: perkType,
        duration: durationMinutes,
        currency,
        amount,
      });

      showSuccessToast(`Started training ${perkType}`);

      // Force a refresh of user data since we know it changed
      await fetchUserData(true);

      // This will use the already fetched user data
      refetchResources();
    } catch (error) {
      showErrorToast(error);
    }
  };

  // We use refreshStateData instead of this function

  // Function to refresh state data
  const refreshStateData = async () => {
    try {
      const stateRes = await stateService.getUserState();
      setMyState(stateRes);
    } catch (error) {
      setMyState(null);
    }
  };

  // Function to refresh party data
  const refreshPartyData = async () => {
    try {
      // Force refresh when explicitly called
      await fetchUserData(true);
    } catch (error) {
      console.error("Error refreshing profile data:", error);
    }
  };

  if (loading || userDataLoading || !profile) {
    return (
      <div className="min-h-screen bg-gray-900">
        <Navbar />
        <div className="flex items-center justify-center h-[calc(100vh-4rem)]">
          <div className="text-neonBlue text-xl">Loading profile...</div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-900">
      <Navbar />
      <div className="max-w-7xl mx-auto px-4 py-8">
        {/* Profile Header */}
        <div className="bg-gray-800 rounded-lg shadow-lg p-6 mb-6">
          <div className="flex flex-col md:flex-row items-center md:items-start">
            {/* Avatar Section */}
            <div className="relative mb-4 md:mb-0">
              <div className="w-24 h-24 bg-gray-700 rounded-full flex items-center justify-center text-4xl text-blue-400 overflow-hidden relative group cursor-pointer">
                {profile?.avatarUrl ? (
                  <img
                    src={profile.avatarUrl}
                    alt="Profile Avatar"
                    className="w-full h-full object-cover rounded-full"
                  />
                ) : (
                  profile?.username?.charAt(0).toUpperCase() || "U"
                )}

                {/* Hover overlay */}
                <div className="absolute inset-0 bg-black bg-opacity-50 rounded-full flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity duration-200">
                <button
                  onClick={handleAvatarClick}
                  disabled={isUploadingAvatar || isDeletingAvatar}
                  className="text-white p-2 rounded-full text-xs disabled:opacity-50 disabled:cursor-not-allowed"
                  title="Upload avatar"
                >
                  <FaCamera />
                </button>
                </div>

                {/* Upload overlay when uploading */}
                {isUploadingAvatar && (
                  <div className="absolute inset-0 bg-black bg-opacity-70 rounded-full flex items-center justify-center">
                    <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-white"></div>
                  </div>
                )}
              </div>

              {/* Avatar action buttons */}
              {/* <div className="absolute -bottom-2 -right-2 flex gap-1">
                <button
                  onClick={handleAvatarClick}
                  disabled={isUploadingAvatar || isDeletingAvatar}
                  className="bg-blue-600 hover:bg-blue-700 text-white p-2 rounded-full text-xs disabled:opacity-50 disabled:cursor-not-allowed"
                  title="Upload avatar"
                >
                  <FaCamera />
                </button>

                {profile?.avatarUrl && (
                  <button
                    onClick={handleDeleteAvatar}
                    disabled={isUploadingAvatar || isDeletingAvatar}
                    className="bg-red-600 hover:bg-red-700 text-white p-2 rounded-full text-xs disabled:opacity-50 disabled:cursor-not-allowed"
                    title="Delete avatar"
                  >
                    {isDeletingAvatar ? (
                      <div className="animate-spin rounded-full h-3 w-3 border-b border-white"></div>
                    ) : (
                      <FaTrash />
                    )}
                  </button>
                )}
              </div> */}

              {/* Hidden file input */}
              <input
                ref={fileInputRef}
                type="file"
                accept="image/jpeg,image/png,image/webp"
                onChange={handleFileSelect}
                className="hidden"
              />
            </div>
            <div className="md:ml-6 text-center md:text-left flex-grow">
              <div className="flex flex-col md:flex-row md:justify-between md:items-start">
                <div>
                  {/* Username with edit functionality */}
                  <div className="flex items-center justify-center md:justify-start mb-2">
                    {isEditingUsername ? (
                      <div className="flex items-center space-x-2">
                        <input
                          type="text"
                          value={newUsername}
                          onChange={(e) => setNewUsername(e.target.value)}
                          onKeyDown={handleUsernameKeyPress}
                          className="w-30 font-bold bg-gray-700 text-white px-3 py-1 rounded border border-gray-600 focus:border-neonBlue focus:outline-none"
                          placeholder="Enter username"
                          maxLength={20}
                          disabled={isUpdatingUsername}
                        />
                        <button
                          onClick={handleSaveUsername}
                          disabled={isUpdatingUsername}
                          className="text-green-400 hover:text-green-300 p-2 disabled:opacity-50"
                          title="Save username"
                        >
                          <FaCheck />
                        </button>
                        <button
                          onClick={handleCancelEdit}
                          disabled={isUpdatingUsername}
                          className="text-red-400 hover:text-red-300 p-2 disabled:opacity-50"
                          title="Cancel edit"
                        >
                          <FaTimes />
                        </button>
                      </div>
                    ) : (
                      <div className="flex items-center space-x-2">
                        <h1 className="text-3xl font-bold text-neonBlue">
                          {profile?.username}
                        </h1>
                        <button
                          onClick={handleEditUsername}
                          className="text-gray-400 hover:text-neonBlue p-1 ml-2"
                          title="Edit username"
                        >
                          <FaEdit />
                        </button>
                      </div>
                    )}
                  </div>
                  <p className="text-gray-400">
                    Member since{" "}
                    {new Date(profile?.createdAt).toLocaleDateString()}
                  </p>
                </div>

                {/* Premium Badge or Get Premium Button and Share Button */}
                <div className="mt-2 md:mt-0 flex items-center justify-center md:justify-end gap-3">
                  {profile?.isPremium ? (
                    <div
                      className={`px-4 py-2 rounded-md flex items-center ${
                        profile?.subscriptionStatus === "cancel_at_period_end"
                          ? "bg-yellow-900/30 border border-yellow-700 text-yellow-300"
                          : "bg-yellow-900/30 border border-yellow-500 text-yellow-400"
                      }`}
                    >
                      <FaCrown className="mr-2" />
                      <div className="flex flex-col">
                        <span className="font-medium">Premium Active</span>
                        {profile?.premiumExpiresAt && (
                          <span className="text-xs">
                            Expires:{" "}
                            {new Date(
                              profile.premiumExpiresAt
                            ).toLocaleDateString()}
                          </span>
                        )}
                      </div>
                    </div>
                  ) : (
                    <Link
                      to="/shop"
                      className="bg-yellow-600 hover:bg-yellow-700 text-white px-4 py-2 rounded-md flex items-center transition-all"
                    >
                      <FaCrown className="mr-2 text-yellow-300" />
                      <span>Get Premium</span>
                    </Link>
                  )}

                  {/* Share Profile Button */}
                  <button
                    onClick={handleShareProfile}
                    className="bg-neonBlue hover:bg-blue-600 text-white px-4 py-2 rounded-md flex items-center transition-all"
                    title="Share your profile"
                  >
                    <FaShare className="mr-2" />
                    <span>Share Profile</span>
                  </button>
                </div>
              </div>

              <div className="mt-4 grid grid-cols-3 gap-4">
                <div className="bg-gray-700 rounded-lg p-3 text-center">
                  <p className="text-gray-400 text-sm">Level</p>
                  <p className="text-white text-xl font-bold">
                    {profile?.level || 1}
                  </p>
                </div>
                <div className="bg-gray-700 rounded-lg p-3 text-center">
                  <p className="text-gray-400 text-sm">Money</p>
                  <p className="text-white text-xl font-bold">
                    {profile?.money?.toLocaleString() || 0}
                  </p>
                </div>
                <div className="bg-gray-700 rounded-lg p-3 text-center">
                  <p className="text-gray-400 text-sm">Gold</p>
                  <p className="text-white text-xl font-bold">
                    {profile?.gold?.toLocaleString() || 0}
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Training Section - LARGE, CENTERED */}
        <div className="bg-gradient-to-r from-gray-800 via-gray-700 to-gray-800 rounded-lg shadow-lg p-6 mb-8 border border-gray-600">
          <div className="flex items-center justify-center mb-6">
            <h2 className="text-2xl font-bold text-center text-neonBlue">
              TRAINING CENTER
            </h2>
            {profile.isPremium && (
              <div className="ml-3 bg-yellow-900/30 border border-yellow-500 text-yellow-400 px-3 py-1 rounded-md flex items-center text-sm">
                <FaCrown className="mr-1" />
                <span>Premium</span>
              </div>
            )}
          </div>

          {isTraining ? (
            <div className="max-w-2xl mx-auto text-center">
              <div className="bg-gray-700 rounded-lg p-5 border border-neonBlue">
                <p className="text-xl text-white mb-2">
                  Currently training:{" "}
                  <span className="text-neonBlue font-bold">
                    {profile.trainingPerk}
                  </span>
                </p>
                <div className="mb-4">
                  <span className="text-gray-400">Time remaining: </span>
                  <div
                    className={`inline-flex bg-gray-800 rounded-lg px-3 py-1 font-mono font-semibold text-white ${
                      countdown && countdown.length <= 3
                        ? "animate-pulse bg-red-900"
                        : ""
                    }`}
                  >
                    {countdown &&
                      countdown.split(" ").map((segment, index) => (
                        <span
                          key={index}
                          className={`mx-1 ${
                            segment.endsWith("s") && countdown.length <= 3
                              ? "text-red-500 text-lg"
                              : segment.endsWith("s")
                              ? "text-red-400"
                              : "text-neonBlue"
                          }`}
                        >
                          {segment}
                        </span>
                      ))}
                  </div>
                </div>
                <div className="w-full bg-gray-800 rounded-full h-3 mb-4">
                  <div
                    className="bg-neonBlue h-3 rounded-full relative overflow-hidden transition-all duration-1000 ease-linear"
                    style={{ width: `${profile.trainingProgress || 0}%` }}
                  >
                    <div className="absolute inset-0 bg-white opacity-20 animate-pulse"></div>
                    <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white to-transparent opacity-30 animate-shimmer"></div>
                  </div>
                </div>
                <p className="text-gray-300 text-sm">
                  Your skills will improve when training is complete
                </p>
              </div>
            </div>
          ) : (
            <div>
              <div className="bg-gray-700 rounded-lg p-4 mb-6 max-w-2xl mx-auto">
                <div className="flex items-center">
                  <div className="w-4 h-4 bg-green-400 rounded-full animate-pulse mr-2"></div>
                  <span className="text-green-400 font-medium">
                    Ready to train - improve your skills now!
                  </span>
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                {["strength", "intelligence", "endurance"].map((perk) => {
                  const currentLevel = profile[perk];
                  const edu = region?.educationIndex || 0;
                  const isPremium = profile.isPremium || false;

                  const goldTime = calculateTrainingTime({
                    perkLevel: currentLevel,
                    eduResidency: edu,
                    currency: "gold",
                    isPremium,
                  });

                  const moneyTime = calculateTrainingTime({
                    perkLevel: currentLevel,
                    eduResidency: edu,
                    currency: "money",
                    isPremium,
                  });

                  const goldCost = calculateTrainingCost({
                    perkLevel: currentLevel,
                    duration: goldTime,
                  }).gold;

                  const moneyCost = calculateTrainingCost({
                    perkLevel: currentLevel,
                    duration: moneyTime,
                  }).money;

                  return (
                    <div
                      key={perk}
                      className="bg-gradient-to-b from-gray-700 to-gray-800 rounded-lg overflow-hidden shadow-lg border border-gray-600"
                    >
                      {/* Card Header */}
                      <div className="bg-gray-600 px-4 py-4 border-b border-gray-500">
                        <h3 className="text-2xl font-bold text-white text-center uppercase tracking-wide">
                          {perk}
                        </h3>
                      </div>

                      {/* Current Level */}
                      <div className="py-6 px-6 text-center border-b border-gray-600">
                        <div className="text-gray-400 text-sm uppercase tracking-wide mb-2">
                          Current Level
                        </div>
                        <div className="text-4xl font-bold text-white">
                          {currentLevel}
                        </div>
                      </div>

                      {/* Training Options */}
                      <div className="p-5">
                        {/* Gold Training Option */}
                        <div className="mb-5 p-4 bg-gray-700 rounded-lg border border-yellow-700">
                          <div className="flex justify-between mb-3">
                            <span className="text-yellow-400 font-semibold text-lg">
                              Gold Training
                            </span>
                            <span className="text-white font-bold text-lg">
                              {goldCost.toLocaleString()} 🪙
                            </span>
                          </div>
                          <div className="flex items-center text-gray-300 text-sm mb-4">
                            <span>Training time: {formatTime(goldTime)}</span>
                            {profile.isPremium && (
                              <span className="ml-2 bg-yellow-900/50 text-yellow-300 text-xs px-2 py-1 rounded-md flex items-center">
                                <FaCrown className="mr-1" size={10} />
                                50% Faster
                              </span>
                            )}
                          </div>
                          <button
                            className="w-full bg-yellow-600 hover:bg-yellow-700 text-white py-3 rounded font-medium transition-all transform hover:scale-105 text-lg"
                            onClick={() =>
                              handleTrain(perk, goldTime, "gold", goldCost)
                            }
                          >
                            Train with Gold
                          </button>
                        </div>

                        {/* Money Training Option */}
                        <div className="p-4 bg-gray-700 rounded-lg border border-green-700">
                          <div className="flex justify-between mb-3">
                            <span className="text-green-400 font-semibold text-lg">
                              Money Training
                            </span>
                            <span className="text-white font-bold text-lg">
                              {moneyCost.toLocaleString()} 💰
                            </span>
                          </div>
                          <div className="flex items-center text-gray-300 text-sm mb-4">
                            <span>Training time: {formatTime(moneyTime)}</span>
                            {profile.isPremium && (
                              <span className="ml-2 bg-yellow-900/50 text-yellow-300 text-xs px-2 py-1 rounded-md flex items-center">
                                <FaCrown className="mr-1" size={10} />
                                50% Faster
                              </span>
                            )}
                          </div>
                          <button
                            className="w-full bg-green-600 hover:bg-green-700 text-white py-3 rounded font-medium transition-all transform hover:scale-105 text-lg"
                            onClick={() =>
                              handleTrain(perk, moneyTime, "money", moneyCost)
                            }
                          >
                            Train with Money
                          </button>
                        </div>
                      </div>
                    </div>
                  );
                })}
              </div>
            </div>
          )}
        </div>

        {/* Other Information - 2 Columns Layout */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* State Information */}
          <div className="bg-gray-800 rounded-lg shadow-lg p-6">
            <h2 className="text-xl font-semibold text-white mb-4">My State</h2>

            {myState ? (
              <div>
                <div className="flex items-center mb-4">
                  <div className="w-16 h-16 bg-gray-700 rounded-full flex items-center justify-center text-2xl text-neonBlue">
                    {myState.name.charAt(0).toUpperCase()}
                  </div>
                  <div className="ml-4">
                    <h3 className="text-lg font-medium text-white">
                      {myState.name}
                    </h3>
                    <p className="text-gray-400">
                      Leader: {myState.leader?.username || "Unknown"}
                    </p>
                  </div>
                </div>

                <div className="space-y-2 mb-4">
                  <div className="flex justify-between">
                    <span className="text-gray-400">Regions:</span>
                    <span className="text-white">
                      {myState.regions?.length || 0}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-400">Treasury:</span>
                    <span className="text-white">
                      {myState.treasury?.toLocaleString() || 0}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-400">Status:</span>
                    <span
                      className={`${
                        myState.isActive ? "text-green-400" : "text-red-400"
                      }`}
                    >
                      {myState.isActive ? "Active" : "Inactive"}
                    </span>
                  </div>
                </div>

                <Link
                  to={`/states/${myState.id}`}
                  className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded block text-center"
                >
                  View State Details
                </Link>
              </div>
            ) : (
              <div className="text-center py-4">
                <p className="text-gray-400 mb-4">
                  You don't belong to any state yet.
                </p>
                <button
                  onClick={() => setIsCreateStateModalOpen(true)}
                  className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded inline-block"
                >
                  Create a State
                </button>
              </div>
            )}
          </div>

          {/* Party Information */}
          <div className="bg-gray-800 rounded-lg shadow-lg p-6">
            <h2 className="text-xl font-semibold text-white mb-4">My Party</h2>

            {profile?.leadingParty ? (
              <div>
                <div className="flex items-center mb-4">
                  <div className="w-16 h-16 bg-gray-700 rounded-full flex items-center justify-center text-2xl text-neonBlue">
                    {profile.leadingParty.name.charAt(0).toUpperCase()}
                  </div>
                  <div className="ml-4">
                    <h3 className="text-lg font-medium text-white">
                      {profile.leadingParty.name}
                    </h3>
                    <p className="text-gray-400">Leader: {profile.username}</p>
                  </div>
                </div>

                <div className="space-y-2 mb-4">
                  <div className="flex justify-between">
                    <span className="text-gray-400">Members:</span>
                    <span className="text-white">
                      {profile.leadingParty.members?.length || 1}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-400">Region:</span>
                    <span className="text-white">
                      {profile.leadingParty.region?.name || "Unknown"}
                    </span>
                  </div>
                </div>

                <Link
                  to={`/party/${profile.leadingParty.id}`}
                  className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded block text-center"
                >
                  View Party Details
                </Link>
              </div>
            ) : profile?.memberOfParty ? (
              <div>
                <div className="flex items-center mb-4">
                  <div className="w-16 h-16 bg-gray-700 rounded-full flex items-center justify-center text-2xl text-neonBlue">
                    {profile.memberOfParty.name.charAt(0).toUpperCase()}
                  </div>
                  <div className="ml-4">
                    <h3 className="text-lg font-medium text-white">
                      {profile.memberOfParty.name}
                    </h3>
                    <p className="text-gray-400">
                      Leader:{" "}
                      {profile.memberOfParty.leader?.username || "Unknown"}
                    </p>
                  </div>
                </div>

                <div className="space-y-2 mb-4">
                  <div className="flex justify-between">
                    <span className="text-gray-400">Members:</span>
                    <span className="text-white">
                      {profile.memberOfParty.members?.length || 1}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-400">Region:</span>
                    <span className="text-white">
                      {profile.memberOfParty.region?.name || "Unknown"}
                    </span>
                  </div>
                </div>

                <Link
                  to={`/party/${profile.memberOfParty.id}`}
                  className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded block text-center"
                >
                  View Party Details
                </Link>
              </div>
            ) : (
              <div className="text-center py-4">
                <p className="text-gray-400 mb-4">
                  You don't belong to any party yet.
                </p>
                <button
                  onClick={() => setIsCreatePartyModalOpen(true)}
                  className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded inline-block"
                >
                  Create a Party
                </button>
                <p className="text-sm text-gray-400 mt-2">Cost: 200 gold</p>
              </div>
            )}
          </div>

          {/* Region Information */}
          <div className="bg-gray-800 rounded-lg shadow-lg p-6">
            <h2 className="text-xl font-semibold text-white mb-4">My Region</h2>

            {profile?.region ? (
              <div className="space-y-4">
                <div className="flex items-center mb-4">
                  <div className="w-16 h-16 bg-gray-700 rounded-full flex items-center justify-center text-2xl text-neonBlue">
                    {profile.region.name.charAt(0).toUpperCase()}
                  </div>
                  <div className="ml-4">
                    <h3 className="text-lg font-medium text-white">
                      {profile.region.name}
                    </h3>
                    <p className="text-gray-400">
                      Population: {profile.region?.population?.toLocaleString()}
                    </p>
                  </div>
                </div>

                <div className="space-y-2">
                  <div className="flex justify-between">
                    <span className="text-gray-400">Resources:</span>
                    <span className="text-white">
                      {profile.region.resources ? (
                        <div className="text-sm">
                          {Object.entries(profile.region.resources).map(
                            ([key, value]) => (
                              <div key={key} className="flex justify-between">
                                <span className="text-gray-400 mr-2">{key}:</span>
                                <span className="text-white">
                                  {typeof value === "object"
                                    ? value.current
                                    : value}
                                </span>
                              </div>
                            )
                          )}
                        </div>
                      ) : (
                        "None"
                      )}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    {/* <span className="text-gray-400">Military Power:</span>
                    <span className="text-white">
                      {profile.region.militaryPower}
                    </span> */}
                  </div>
                  <div className="flex justify-between">
                    {/* <span className="text-gray-400">Status:</span>
                    <span
                      className={`${
                        profile.region.status === "state_member"
                          ? "text-green-400"
                          : profile.region.status === "independent"
                          ? "text-yellow-400"
                          : "text-red-400"
                      }`}
                    >
                      {profile.region.status.charAt(0).toUpperCase() +
                        profile.region.status.slice(1)}
                    </span> */}
                  </div>
                </div>
                <Link
                  to={`/regions/${profile.region.id}`}
                  className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded block text-center"
                >
                  View Region Details
                </Link>
              </div>
            ) : (
              <div className="text-center py-4">
                <p className="text-gray-400">
                  You don't have a region assigned yet.
                </p>
              </div>
            )}
          </div>

          {/* War Statistics */}
          <div className="bg-gray-800 rounded-lg shadow-lg p-6 mb-6">
            <UserWarStats />
          </div>

          {/* War Information */}
          <div className="bg-gray-800 rounded-lg shadow-lg p-6">
            <h2 className="text-xl font-semibold text-white mb-4">My Wars</h2>

            {myWars.length > 0 ? (
              <div className="max-h-80 overflow-y-auto scrollbar-thin scrollbar-thumb-gray-600 scrollbar-track-gray-800">
                {myWars.map((war) => (
                  <div
                    key={war.id}
                    className="border-b border-gray-700 pb-4 last:border-0 last:pb-0"
                  >
                    <h3 className="text-lg font-medium text-white mb-2">
                      {war.warType} War
                    </h3>
                    <div className="space-y-1 mb-2">
                      <div className="flex justify-between">
                        <span className="text-gray-400">Attacker:</span>
                        <p className="text-gray-300 mb-1">
                          <Link
                            to={`/regions/${war.attackerRegion.id}`}
                            className="text-neonBlue hover:text-blue-400"
                          >
                            {war.attackerRegion.name}
                          </Link>
                        </p>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-400">Defender:</span>
                        <p className="text-gray-300 mb-1">
                          <Link
                            to={`/regions/${war.defenderRegion.id}`}
                            className="text-neonBlue hover:text-blue-400"
                          >
                            {war.defenderRegion.name}
                          </Link>
                        </p>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-400">Status:</span>
                        <span className="text-white">{war.status}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-400">Started:</span>
                        <span className="text-white">
                          {new Date(war.declaredAt).toLocaleDateString()}
                        </span>
                      </div>
                    </div>
                    <Link
                      to={`/wars/${war.id}`}
                      className="text-red-400 hover:text-red-300 text-sm"
                    >
                      View War Details
                    </Link>
                  </div>
                ))}
              </div>
            ) : (
              <div className="text-center py-4">
                <p className="text-gray-400 mb-4">
                  You're not participating in any wars.
                </p>
                <Link
                  to="/wars/new"
                  className="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded inline-block"
                >
                  Declare War
                </Link>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Share Profile Modal */}
      {showShareModal && (
        <div className="fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-50 p-4">
          <div className="bg-gray-800 rounded-lg shadow-2xl max-w-md w-full p-6">
            <div className="flex items-center justify-between mb-4">
              <h2 className="text-xl font-bold text-white flex items-center">
                <FaShare className="mr-2 text-neonBlue" />
                Share Your Profile
              </h2>
              <button
                onClick={() => setShowShareModal(false)}
                className="text-gray-400 hover:text-white transition-colors p-1"
              >
                <FaTimes className="w-5 h-5" />
              </button>
            </div>

            <div className="mb-4">
              <p className="text-gray-300 mb-3">
                Share your profile with others using this link:
              </p>
              <div className="bg-gray-700 rounded-lg p-3 border border-gray-600">
                <div className="flex items-center justify-between">
                  <span className="text-neonBlue text-sm font-mono break-all mr-2">
                    {getShareableProfileUrl()}
                  </span>
                  <button
                    onClick={handleCopyProfileUrl}
                    className={`flex items-center px-3 py-1 rounded text-sm transition-all ${
                      copySuccess
                        ? "bg-green-600 text-white"
                        : "bg-neonBlue hover:bg-blue-600 text-white"
                    }`}
                    title="Copy to clipboard"
                  >
                    {copySuccess ? (
                      <>
                        <FaCheck className="mr-1" />
                        Copied!
                      </>
                    ) : (
                      <>
                        <FaCopy className="mr-1" />
                        Copy
                      </>
                    )}
                  </button>
                </div>
              </div>
            </div>

            <div className="text-center">
              <button
                onClick={() => setShowShareModal(false)}
                className="bg-gray-700 hover:bg-gray-600 text-white px-4 py-2 rounded-md transition-colors"
              >
                Close
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Create State Modal */}
      <CreateStateModal
        isOpen={isCreateStateModalOpen}
        onClose={() => setIsCreateStateModalOpen(false)}
        onSuccess={() => {
          refreshStateData();
          setIsCreateStateModalOpen(false);
        }}
      />

      {/* Create Party Modal */}
      <CreatePartyModal
        isOpen={isCreatePartyModalOpen}
        onClose={() => setIsCreatePartyModalOpen(false)}
        onSuccess={() => {
          refreshPartyData();
          setIsCreatePartyModalOpen(false);
        }}
      />

      <Footer />
    </div>
  );
}
