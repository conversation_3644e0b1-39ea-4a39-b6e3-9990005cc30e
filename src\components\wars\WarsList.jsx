import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { warService } from '../../services/api/war.service';
import { formatDate } from '../../utils/formatDate';
import { stateService } from '../../services/api/state.service';
const WarsList = () => {
  const [wars, setWars] = useState([]);
  const [activeWars, setActiveWars] = useState([]);
  const [currentStateWars, setCurrentStateWars] = useState([]);
  const [myState, setMyState] = useState();
  const [myWars, setMyWars] = useState([]);
  const [activeTab, setActiveTab] = useState('state');
  const [lastCreatedWarId, setLastCreatedWarId] = useState(null);
  const [loading, setLoading] = useState(false);
  const [refreshing, setRefreshing] = useState(false);
  const [error, setError] = useState(null);
  const [loadedTabs, setLoadedTabs] = useState(new Set());

  const fetchStateData = async () => {
     try {
      setLoading(true);
       const stateResponse = await stateService.getUserState();
       setMyState(stateResponse);
     }  
      catch (error) {
       setMyState(null);
       return;
     }
   };

  const fetchWarsForTab = async (tab, isRefreshing = false) => {
    try {
      if (isRefreshing) {
        setRefreshing(true);
      } else {
        setLoading(true);
      }

      let warData;
      switch (tab) {
        case 'all':
          warData = await warService.getAllWars();
          setWars(warData);
          break;
        case 'active':
          warData = await warService.getActiveWars();
          setActiveWars(warData);
          break;
        case 'my':
          warData = await warService.getMyWars();
          setMyWars(warData);
          break;
        case 'state':
        default:
          if (myState && myState.id) {
            warData = await warService.getCurrentStateWars(myState.id);
            setCurrentStateWars(warData);
          }
          break;
      }

      setLoadedTabs(prev => new Set([...prev, tab]));
      setError(null);
    } catch (err) {
      console.error(`Error fetching wars for ${tab} tab:`, err);
      setError('Failed to load wars. Please try again later.');
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  const handleRefresh = () => {
    fetchWarsForTab(activeTab, true);
  };

  const handleTabChange = (tab) => {
    setActiveTab(tab);
    // Only fetch if this tab hasn't been loaded yet or if it's a refresh
    if (!loadedTabs.has(tab)) {
      fetchWarsForTab(tab);
    }
  };

  useEffect(() => {
    fetchStateData();


    // Check if there's a lastCreatedWarId in localStorage
    const storedWarId = localStorage.getItem('lastCreatedWarId');
    if (storedWarId) {
      setLastCreatedWarId(storedWarId);
      // Switch to the State Wars tab to show the new war
      setActiveTab('state');
      // Clear it after retrieving
      localStorage.removeItem('lastCreatedWarId');
    }
  }, []);

  // Load the initial tab when myState is ready
  useEffect(() => {
    if (myState !== undefined && !loadedTabs.has(activeTab)) {
      fetchWarsForTab(activeTab);
    }
  }, [myState, activeTab, loadedTabs]);

  const getWarStatusClass = (status) => {
    switch (status) {
      case 'pending':
        return 'bg-yellow-600 text-white';
      case 'active':
        return 'bg-green-600 text-white';
      case 'revolution_phase':
        return 'bg-red-600 text-white';
      case 'ended':
        return 'bg-gray-600 text-white';
      default:
        return 'bg-gray-600 text-white';
    }
  };

  const renderWarsList = (warsList) => {
    if (warsList.length === 0) {
      return (
        <div className="bg-gray-800 border border-gray-700 rounded-lg p-8 text-center mt-6">
          <p className="text-gray-300 text-lg">No wars found in this category.</p>
          {activeTab === 'my' && (
            <Link to="/wars/new" className="mt-4 inline-block bg-blue-600 hover:bg-blue-700 px-4 py-2 rounded-md">
              Declare a New War
            </Link>
          )}
        </div>
      );
    }
      if (myState.id === null) {
        return (
        <div className="bg-gray-800 border border-gray-700 rounded-lg p-8 text-center mt-6">
          <p className="text-gray-300 text-lg">You are in a region that is not in any state.</p>
          {activeTab === 'state' && (
            <Link to="/wars/new" className="mt-4 inline-block bg-blue-600 hover:bg-blue-700 px-4 py-2 rounded-md">
              Declare a New War
            </Link>
          )}
        </div>
      );
      }
    return (
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mt-4">
        {warsList.map(war => (
          <div
            key={war.id}
            className={`bg-gray-800 p-5 rounded-lg shadow-lg border ${war.id === lastCreatedWarId ? 'border-yellow-500 ring-2 ring-yellow-500' : 'border-gray-700'} hover:border-gray-500 transition-all`}
          >
            <div className="flex justify-between items-start mb-3">
              <h3 className="text-lg font-medium text-white">
                {war.warType.toUpperCase() || 'Unknown'} WAR
                {war.id === lastCreatedWarId && (
                  <span className="ml-2 bg-yellow-500 text-black text-xs px-2 py-1 rounded-full animate-pulse">
                    New
                  </span>
                )}
              </h3>
              <span className={`px-3 py-1 rounded-full text-xs font-medium ${getWarStatusClass(war.status)}`}>
                {war.status || 'Unknown'}
              </span>
            </div>

            <div className="mb-4 text-gray-300">
              <p className="text-sm mb-1">Target: {war.warTarget || 'Unknown'}</p>
              <p className="text-sm mb-1">
                Started: {formatDate(war.declaredAt)}
              </p>
              {war.endedAt && (
                <p className="text-sm">
                  Ended: {formatDate(war.endedAt)}
                </p>
              )}
            </div>

            <div className="flex justify-between text-sm mb-2">
                <span className="text-gray-300">
                  <span className="font-medium text-white">Attacking:</span> {war.attackerRegion?.name || 'Unknown'}
                  {war.attackerState && <span> ({war.attackerState.name})</span>}
                </span>
                <span className="text-gray-300">
                  <span className="font-medium text-white">Defending:</span> {war.defenderRegion?.name || 'Unknown'}
                  {war.defenderState && <span> ({war.defenderState.name})</span>}
                </span>
              </div>

            <div className="mb-4">
              <div className="flex justify-between text-sm text-gray-300 mb-2">
                <span>Attacks: {war.participants?.attackers?.length || 0}</span>
                <span>Defends: {war.participants?.defenders?.length || 0}</span>
              </div>

              <div className="w-full bg-gray-600 rounded-full h-3 mb-2">
                <div
                  className="bg-red-600 h-3 rounded-full"
                  style={{ width: `${((war.attackerGroundDamage || 0) / ((war.attackerGroundDamage || 0) + (war.defenderGroundDamage || 0) || 1)) * 100}%` }}
                ></div>
              </div>

              <div className="flex justify-between text-xs text-gray-400">
                <span>Attacker: {war.attackerGroundDamage || 0} DMG</span>
                <span>Defender: {war.defenderGroundDamage + war.damageRequirement|| 0} DMG</span>
              </div>
            </div>

            <div className="mt-4 flex justify-center">
              <Link
                to={`/wars/${war.id}`}
                className="w-full bg-blue-600 hover:bg-blue-700 py-2 px-4 rounded-md text-center transition-colors font-medium"
              >
                View Details
              </Link>
            </div>
          </div>
        ))}
      </div>
    );
  };

  const getDisplayedWars = () => {
    switch (activeTab) {
      case 'all':
        return wars;
      case 'active':
        return activeWars;
      case 'my':
        return myWars;
      default:
        return currentStateWars;
    }
  };

  // Show loading only when actively loading and not refreshing
  if (loading && !refreshing) return (
    <div className="flex justify-center items-center mt-12">
      <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
      <span className="ml-3 text-white">Loading wars...</span>
    </div>
  );

  // Show a message if no data has been loaded for the current tab yet
  if (!loadedTabs.has(activeTab) && !loading) {
    return (
      <div className="container mx-auto p-4">
        <div className="flex flex-col sm:flex-row justify-between items-center mb-6 gap-4">
          <h1 className="text-3xl font-bold text-white">Wars</h1>
          <Link
            to="/wars/new"
            className="bg-red-600 hover:bg-red-700 px-6 py-2 rounded-md font-medium transition-colors"
          >
            Declare War
          </Link>
        </div>
        <div className="mb-8">
          <div className="flex justify-between items-center mb-2">
            <div className="flex flex-wrap border-b border-gray-700">
              <button
                className={`py-3 px-6 font-medium ${activeTab === 'state' ? 'border-b-2 border-blue-500 text-blue-400' : 'text-gray-400 hover:text-gray-300'}`}
                onClick={() => handleTabChange('state')}
               >
                State Wars
                <span className="ml-2 bg-orange-900 text-red-100 text-xs px-2 py-1 rounded-full">{currentStateWars.length}</span>
              </button>
              <button
                className={`py-3 px-6 font-medium ${activeTab === 'active' ? 'border-b-2 border-blue-500 text-blue-400' : 'text-gray-400 hover:text-gray-300'}`}
                onClick={() => handleTabChange('active')}
              >
                Active Wars
                <span className="ml-2 bg-red-900 text-red-100 text-xs px-2 py-1 rounded-full">{activeWars.length}</span>
              </button>
              <button
                className={`py-3 px-6 font-medium ${activeTab === 'my' ? 'border-b-2 border-blue-500 text-blue-400' : 'text-gray-400 hover:text-gray-300'}`}
                onClick={() => handleTabChange('my')}
              >
                My Wars
                <span className="ml-2 bg-blue-900 text-blue-100 text-xs px-2 py-1 rounded-full">{myWars.length}</span>
              </button>
              <button
                className={`py-3 px-6 font-medium ${activeTab === 'all' ? 'border-b-2 border-blue-500 text-blue-400' : 'text-gray-400 hover:text-gray-300'}`}
                onClick={() => handleTabChange('all')}
              >
                All Wars
                <span className="ml-2 bg-gray-700 text-gray-300 text-xs px-2 py-1 rounded-full">{wars.length}</span>
              </button>
            </div>
          </div>
        </div>
        <div className="bg-gray-800 border border-gray-700 rounded-lg p-8 text-center mt-6">
          <p className="text-gray-300 text-lg">Click on a tab to load wars data.</p>
        </div>
      </div>
    );
  }

  if (error) return (
    <div className="bg-red-900 border-l-4 border-red-500 text-red-100 p-4 rounded mt-6" role="alert">
      <p className="font-medium">Error</p>
      <p>{error}</p>
    </div>
  );

  return (
    <div className="container mx-auto p-4 relative">
      {refreshing && (
        <div className="absolute inset-0 bg-black bg-opacity-50 flex items-center justify-center z-10 rounded-lg">
          <div className="bg-gray-800 p-6 rounded-lg shadow-lg flex items-center">
            <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-blue-500 mr-3"></div>
            <span className="text-white">Refreshing wars...</span>
          </div>
        </div>
      )}
      <div className="flex flex-col sm:flex-row justify-between items-center mb-6 gap-4">
        <h1 className="text-3xl font-bold text-white">Wars</h1>
        <Link
          to="/wars/new"
          className="bg-red-600 hover:bg-red-700 px-6 py-2 rounded-md font-medium transition-colors"
        >
          Declare War
        </Link>
      </div>

      {lastCreatedWarId && (
        <div className="bg-yellow-900 border-l-4 border-yellow-500 text-yellow-100 p-4 rounded-md mb-6">
          <div className="flex">
            <div className="flex-shrink-0">
              <svg className="h-5 w-5 text-yellow-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
              </svg>
            </div>
            <div className="ml-3">
              <p className="text-sm">
                War successfully declared! Your new war is highlighted below.
              </p>
            </div>
          </div>
        </div>
      )}

      <div className="mb-8">
        <div className="flex justify-between items-center mb-2">
          <div className="flex flex-wrap border-b border-gray-700">
            <button
              className={`py-3 px-6 font-medium ${activeTab === 'state' ? 'border-b-2 border-blue-500 text-blue-400' : 'text-gray-400 hover:text-gray-300'}`}
              onClick={() => handleTabChange('state')}
             >
              State Wars
              <span className="ml-2 bg-orange-900 text-red-100 text-xs px-2 py-1 rounded-full">{currentStateWars.length}</span>
            </button>
            <button
              className={`py-3 px-6 font-medium ${activeTab === 'active' ? 'border-b-2 border-blue-500 text-blue-400' : 'text-gray-400 hover:text-gray-300'}`}
              onClick={() => handleTabChange('active')}
            >
              Active Wars
              {activeWars.length > 0 && (
                <span className="ml-2 bg-red-900 text-red-100 text-xs px-2 py-1 rounded-full">{activeWars.length}</span>
              )}
            </button>
            <button
              className={`py-3 px-6 font-medium ${activeTab === 'my' ? 'border-b-2 border-blue-500 text-blue-400' : 'text-gray-400 hover:text-gray-300'}`}
              onClick={() => handleTabChange('my')}
            >
              My Wars
              {myWars.length > 0 && (
                <span className="ml-2 bg-blue-900 text-blue-100 text-xs px-2 py-1 rounded-full">{myWars.length}</span>
              )}
            </button>
            <button
              className={`py-3 px-6 font-medium ${activeTab === 'all' ? 'border-b-2 border-blue-500 text-blue-400' : 'text-gray-400 hover:text-gray-300'}`}
              onClick={() => handleTabChange('all')}
            >
              All Wars
              {wars.length > 0 && (
                <span className="ml-2 bg-gray-700 text-gray-300 text-xs px-2 py-1 rounded-full">{wars.length}</span>
              )}
            </button>
          </div>

          <button
            onClick={handleRefresh}
            disabled={refreshing || loading}
            className="flex items-center gap-2 bg-gray-700 hover:bg-gray-600 text-white px-4 py-2 rounded-md transition-colors"
          >
            <svg xmlns="http://www.w3.org/2000/svg" className={`h-5 w-5 ${refreshing ? 'animate-spin' : ''}`} fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
            </svg>
            {refreshing ? 'Refreshing...' : 'Refresh'}
          </button>
        </div>
      </div>

      {renderWarsList(getDisplayedWars())}
    </div>
  );
};

export default WarsList;